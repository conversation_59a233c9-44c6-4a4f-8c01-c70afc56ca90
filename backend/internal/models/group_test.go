package models

import (
	"testing"
	"time"
)

func TestGroupModel(t *testing.T) {
	group := Group{
		ID:          1,
		Title:       "Test Group",
		Description: "This is a test group.",
		CreatorID:   101,
		CreatedAt:   time.Now(),
	}

	if group.ID != 1 {
		t.<PERSON><PERSON><PERSON>("Expected ID 1, got %d", group.ID)
	}
	if group.Title != "Test Group" {
		t.<PERSON>("Expected Title 'Test Group', got %s", group.Title)
	}
	if group.Description != "This is a test group." {
		t.<PERSON>("Expected Description 'This is a test group.', got %s", group.Description)
	}
	if group.CreatorID != 101 {
		t.<PERSON><PERSON><PERSON>("Expected CreatorID 101, got %d", group.CreatorID)
	}
}
