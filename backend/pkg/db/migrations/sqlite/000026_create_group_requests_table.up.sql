-- Create Group_Requests table
CREATE TABLE IF NOT EXISTS Group_Requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    group_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('pending', 'approved', 'rejected')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (group_id) REFERENCES Groups(id) ON DELETE CASCADE,
    FOREIG<PERSON> KEY (user_id) REFERENCES Users(id) ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS idx_group_requests_group_id ON Group_Requests(group_id);
CREATE INDEX IF NOT EXISTS idx_group_requests_user_id ON Group_Requests(user_id);