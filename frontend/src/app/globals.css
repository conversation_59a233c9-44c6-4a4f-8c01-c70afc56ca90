@import "tailwindcss";

:root {
  /* Background Colors */
  --primary-background: #010c66;
  --secondary-background: #2a2a86;
  --tertiary-background: #c5c6db;

  /* Text Color */
  --primary-text: #ffffff;
  --secondary-text: #afafed;
  --tertiary-text: #3f3fd3;
  --quaternary-text: #2a2a86;
  --quinary-text: #010c66;

  /* Accent Colors */
  --primary-accent: #ffd700;

  /* Status Colors */
  --sucess-color: #00ff00;
  --warning-color: #ff4444;
  

  /* Interactive Colors */
  --hover-background: #3a3a9a;
  --border-color: #4a4ab0;
}

/* Body Background with Texture */
body {
  background-color: var(--primary-background);
  background-image: url('/assets/background_textures/grain.png');
  background-blend-mode: overlay;
  font-family: Arial, Helvetica, sans-serif;
}

/* Text wrapping utilities */
.text-wrap {
  word-wrap: break-word;
  overflow-wrap: anywhere;
  hyphens: auto;
}

/* Screen reader only utility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skeleton loading styles with reduced motion support */
.post-skeleton {
  /* Ensure consistent dimensions to prevent layout shifts */
  min-height: 140px; /* Approximate height of a typical post */
}

/* Smooth fade-in for post content */
.post-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (prefers-reduced-motion: reduce) {
  .post-skeleton {
    animation: none;
  }
  
  .post-skeleton .loading-skeleton {
    opacity: 0.7;
  }
  
  .post-content {
    animation: none;
  }
}

/* @theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
} */

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}
